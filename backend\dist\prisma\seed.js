"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const auth_1 = require("../src/lib/auth");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Starting database seeding...');
    // Create default admin user
    const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123';
    const adminName = process.env.DEFAULT_ADMIN_NAME || 'System Administrator';
    const hashedPassword = await auth_1.AuthService.hashPassword(adminPassword);
    const admin = await prisma.user.upsert({
        where: { email: adminEmail },
        update: {},
        create: {
            name: adminN<PERSON>,
            email: adminEmail,
            phone: '+919999999999',
            password: hashedPassword,
            role: client_1.UserRole.SUPER_ADMIN,
            isActive: true,
            timezone: 'Asia/Kolkata',
            language: 'en',
        },
    });
    console.log('✅ Created admin user:', admin.email);
    // Create sample properties
    const properties = [
        {
            name: 'Jubilee Hills Residence',
            type: client_1.PropertyType.RESIDENTIAL,
            address: 'Road No. 36, Jubilee Hills, Hyderabad, Telangana 500033',
            description: 'Luxury residential property in prime Jubilee Hills location',
            latitude: 17.4239,
            longitude: 78.4738,
            images: [],
        },
        {
            name: 'Banjara Hills Office Complex',
            type: client_1.PropertyType.OFFICE,
            address: 'Road No. 12, Banjara Hills, Hyderabad, Telangana 500034',
            description: 'Modern office complex with state-of-the-art facilities',
            latitude: 17.4126,
            longitude: 78.4482,
            images: [],
        },
        {
            name: 'Gandipet Construction Site',
            type: client_1.PropertyType.CONSTRUCTION,
            address: 'Gandipet, Hyderabad, Telangana 500075',
            description: 'Ongoing residential construction project',
            latitude: 17.3616,
            longitude: 78.2747,
            images: [],
        },
    ];
    const createdProperties = [];
    for (const propertyData of properties) {
        // Check if property already exists by name
        const existingProperty = await prisma.property.findFirst({
            where: { name: propertyData.name },
        });
        const property = existingProperty || await prisma.property.create({
            data: propertyData,
        });
        // Create system statuses for each property
        const systemTypes = ['WATER', 'ELECTRICITY', 'SECURITY', 'INTERNET', 'OTT', 'MAINTENANCE'];
        for (const systemType of systemTypes) {
            await prisma.systemStatus.upsert({
                where: {
                    propertyId_systemType: {
                        propertyId: property.id,
                        systemType,
                    },
                },
                update: {},
                create: {
                    propertyId: property.id,
                    systemType,
                    status: 'OPERATIONAL',
                    description: `${systemType.toLowerCase()} system operational`,
                    healthScore: Math.floor(Math.random() * 20) + 80, // 80-100
                },
            });
        }
        createdProperties.push(property);
        console.log('✅ Created property:', property.name);
    }
    // Create sample users with different roles
    const users = [
        {
            name: 'Property Manager',
            email: '<EMAIL>',
            phone: '+919999999998',
            role: client_1.UserRole.PROPERTY_MANAGER,
            assignedProperties: [createdProperties[0].id, createdProperties[1].id],
        },
        {
            name: 'Office Manager',
            email: '<EMAIL>',
            phone: '+919999999997',
            role: client_1.UserRole.OFFICE_MANAGER,
            assignedProperties: [],
        },
        {
            name: 'Security Personnel',
            email: '<EMAIL>',
            phone: '+919999999996',
            role: client_1.UserRole.SECURITY_PERSONNEL,
            assignedProperties: [createdProperties[0].id],
        },
        {
            name: 'Maintenance Staff',
            email: '<EMAIL>',
            phone: '+919999999995',
            role: client_1.UserRole.MAINTENANCE_STAFF,
            assignedProperties: [createdProperties[0].id, createdProperties[1].id],
        },
        {
            name: 'Construction Supervisor',
            email: '<EMAIL>',
            phone: '+919999999994',
            role: client_1.UserRole.CONSTRUCTION_SUPERVISOR,
            assignedProperties: [createdProperties[2].id],
        },
    ];
    for (const userData of users) {
        const hashedUserPassword = await auth_1.AuthService.hashPassword('password123');
        const user = await prisma.user.upsert({
            where: { email: userData.email },
            update: {},
            create: {
                name: userData.name,
                email: userData.email,
                phone: userData.phone,
                password: hashedUserPassword,
                role: userData.role,
                isActive: true,
                timezone: 'Asia/Kolkata',
                language: 'en',
            },
        });
        // Assign properties to user
        for (const propertyId of userData.assignedProperties) {
            await prisma.userProperty.upsert({
                where: {
                    userId_propertyId: {
                        userId: user.id,
                        propertyId,
                    },
                },
                update: {},
                create: {
                    userId: user.id,
                    propertyId,
                },
            });
        }
        console.log('✅ Created user:', user.email);
    }
    // Create sample offices
    const offices = [
        {
            name: 'Main Office - Hyderabad',
            type: client_1.OfficeType.OFFICE,
            address: 'HITEC City, Hyderabad, Telangana 500081',
            latitude: 17.4435,
            longitude: 78.3772,
            workingHours: {
                monday: { start: '09:00', end: '18:00' },
                tuesday: { start: '09:00', end: '18:00' },
                wednesday: { start: '09:00', end: '18:00' },
                thursday: { start: '09:00', end: '18:00' },
                friday: { start: '09:00', end: '18:00' },
                saturday: { start: '09:00', end: '14:00' },
                sunday: { closed: true },
            },
        },
        {
            name: 'Gandipet Construction Site',
            type: client_1.OfficeType.CONSTRUCTION_SITE,
            address: 'Gandipet, Hyderabad, Telangana 500075',
            latitude: 17.3616,
            longitude: 78.2747,
            workingHours: {
                monday: { start: '08:00', end: '17:00' },
                tuesday: { start: '08:00', end: '17:00' },
                wednesday: { start: '08:00', end: '17:00' },
                thursday: { start: '08:00', end: '17:00' },
                friday: { start: '08:00', end: '17:00' },
                saturday: { start: '08:00', end: '17:00' },
                sunday: { closed: true },
            },
        },
    ];
    const createdOffices = [];
    for (const officeData of offices) {
        // Check if office already exists by name
        const existingOffice = await prisma.office.findFirst({
            where: { name: officeData.name },
        });
        const office = existingOffice || await prisma.office.create({
            data: officeData,
        });
        createdOffices.push(office);
        console.log('✅ Created office:', office.name);
    }
    // Create sample employees
    const employees = [
        {
            officeId: createdOffices[0].id,
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+919999999993',
            employeeId: 'EMP001',
            designation: 'Software Engineer',
            department: 'IT',
            joinDate: new Date('2023-01-15'),
        },
        {
            officeId: createdOffices[0].id,
            name: 'Jane Smith',
            email: '<EMAIL>',
            phone: '+919999999992',
            employeeId: 'EMP002',
            designation: 'Project Manager',
            department: 'Operations',
            joinDate: new Date('2023-02-01'),
        },
        {
            officeId: createdOffices[1].id,
            name: 'Mike Johnson',
            email: '<EMAIL>',
            phone: '+919999999991',
            employeeId: 'EMP003',
            designation: 'Site Engineer',
            department: 'Construction',
            joinDate: new Date('2023-03-01'),
        },
    ];
    for (const employeeData of employees) {
        const employee = await prisma.employee.upsert({
            where: { employeeId: employeeData.employeeId },
            update: {},
            create: employeeData,
        });
        console.log('✅ Created employee:', employee.name);
    }
    // Create sample alerts
    const alerts = [
        {
            propertyId: createdProperties[0].id,
            title: 'Water Tank Level Low',
            message: 'Water tank level is below 20%. Immediate attention required.',
            severity: 'HIGH',
            category: 'WATER',
            metadata: {
                tankLevel: 18,
                threshold: 20,
            },
        },
        {
            propertyId: createdProperties[1].id,
            title: 'Security Camera Offline',
            message: 'Camera #3 in parking area is not responding.',
            severity: 'MEDIUM',
            category: 'SECURITY',
            metadata: {
                cameraId: 'CAM003',
                location: 'Parking Area',
            },
        },
    ];
    for (const alertData of alerts) {
        const alert = await prisma.alert.create({
            data: alertData,
        });
        console.log('✅ Created alert:', alert.title);
    }
    // Create sample departments
    const departments = [
        {
            name: 'Electricity',
            description: 'Electrical systems and power management',
        },
        {
            name: 'Water',
            description: 'Water supply and plumbing systems',
        },
        {
            name: 'Security',
            description: 'Security systems and surveillance',
        },
        {
            name: 'Internet',
            description: 'Network and internet connectivity',
        },
        {
            name: 'Generator',
            description: 'Backup power generation systems',
        },
        {
            name: 'CCTV',
            description: 'Closed-circuit television monitoring',
        },
        {
            name: 'Maintenance',
            description: 'General maintenance and repairs',
        },
        {
            name: 'OTTs',
            description: 'Over-the-top streaming services',
        },
    ];
    const createdDepartments = [];
    for (const deptData of departments) {
        const department = await prisma.department.upsert({
            where: { name: deptData.name },
            update: {},
            create: deptData,
        });
        createdDepartments.push(department);
        console.log('✅ Created department:', department.name);
    }
    // Create sample maintenance issues
    const maintenanceIssues = [
        {
            title: 'Invertor Maintenance',
            description: 'Monthly maintenance check for invertor system',
            departmentId: createdDepartments.find(d => d.name === 'Electricity').id,
            propertyId: createdProperties[0].id,
            priority: 'LOW',
            status: 'RESOLVED',
            startDate: new Date('2025-05-01'),
            expectedEndDate: new Date('2025-05-31'),
            reportedBy: admin.id,
            isRecurring: true,
            recurrenceType: 'MONTHLY',
            nextDueDate: new Date('2025-06-01'),
        },
        {
            title: 'CCTV Annual Maintenance',
            description: 'Annual maintenance and inspection of CCTV systems',
            departmentId: createdDepartments.find(d => d.name === 'Security').id,
            propertyId: createdProperties[0].id,
            priority: 'LOW',
            status: 'RESOLVED',
            startDate: new Date('2025-06-01'),
            expectedEndDate: new Date('2025-06-30'),
            reportedBy: admin.id,
            isRecurring: true,
            recurrenceType: 'YEARLY',
        },
        {
            title: 'ACs - Annual Cleaning and Maintenance',
            description: 'Annual cleaning and maintenance of air conditioning units',
            departmentId: createdDepartments.find(d => d.name === 'Electricity').id,
            propertyId: createdProperties[0].id,
            priority: 'LOW',
            status: 'CLOSED',
            startDate: new Date('2025-05-01'),
            expectedEndDate: new Date('2025-05-31'),
            reportedBy: admin.id,
            isRecurring: true,
            recurrenceType: 'YEARLY',
        },
        {
            title: 'Generator - Annual Preventive Maintenance',
            description: 'Annual preventive maintenance for backup generator',
            departmentId: createdDepartments.find(d => d.name === 'Generator').id,
            propertyId: createdProperties[0].id,
            priority: 'MEDIUM',
            status: 'RESOLVED',
            startDate: new Date('2024-11-30'),
            expectedEndDate: new Date('2024-12-31'),
            reportedBy: admin.id,
            isRecurring: true,
            recurrenceType: 'YEARLY',
        },
        {
            title: 'Water Tank Automation - Dry Run on 24th May 2025',
            description: 'Scheduled dry run test for water tank automation system',
            departmentId: createdDepartments.find(d => d.name === 'Water').id,
            propertyId: createdProperties[0].id,
            priority: 'HIGH',
            status: 'RESOLVED',
            startDate: new Date('2025-05-24'),
            expectedEndDate: new Date('2025-05-24'),
            reportedBy: admin.id,
            isRecurring: false,
        },
        {
            title: 'Water Tank - Semi Annual Cleaning',
            description: 'Semi-annual cleaning and maintenance of water tanks',
            departmentId: createdDepartments.find(d => d.name === 'Water').id,
            propertyId: createdProperties[0].id,
            priority: 'MEDIUM',
            status: 'RESOLVED',
            startDate: new Date('2025-05-16'),
            expectedEndDate: new Date('2025-05-31'),
            reportedBy: admin.id,
            isRecurring: true,
            recurrenceType: 'QUARTERLY',
        },
        {
            title: 'Lighting - All external lighting in and around the House are functional',
            description: 'Check and ensure all external lighting systems are working properly',
            departmentId: createdDepartments.find(d => d.name === 'Electricity').id,
            propertyId: createdProperties[0].id,
            priority: 'LOW',
            status: 'CLOSED',
            startDate: new Date('2025-05-01'),
            expectedEndDate: new Date('2025-05-31'),
            reportedBy: admin.id,
            isRecurring: true,
            recurrenceType: 'YEARLY',
        },
        {
            title: 'Generator Automation',
            description: 'Automated generator system monitoring and maintenance',
            departmentId: createdDepartments.find(d => d.name === 'Generator').id,
            propertyId: createdProperties[0].id,
            priority: 'MEDIUM',
            status: 'OPEN',
            startDate: new Date('2025-05-12'),
            expectedEndDate: new Date('2025-06-12'),
            reportedBy: admin.id,
            isRecurring: false,
        },
    ];
    for (const issueData of maintenanceIssues) {
        const issue = await prisma.maintenanceIssue.create({
            data: issueData,
        });
        console.log('✅ Created maintenance issue:', issue.title);
    }
    // Create sample maintenance functions
    const maintenanceFunctions = [
        {
            name: 'Attendance - Office',
            subFunction: 'Staff Presence',
            departmentId: createdDepartments.find(d => d.name === 'Maintenance').id,
            propertyId: createdProperties[0].id,
            input: 'Check-in/check-out logs',
            process: 'Compare vs shift schedule',
            output: 'Present/Absent',
            thresholdLimits: 'Max 1 absence/week',
            responsibleAgent: 'Office Incharge',
        },
        {
            name: 'Attendance - Site',
            subFunction: 'Security Presence',
            departmentId: createdDepartments.find(d => d.name === 'Security').id,
            propertyId: createdProperties[0].id,
            input: 'Log entry/exit',
            process: 'Auto-capture + manual verification',
            output: 'On Duty/Off Duty',
            thresholdLimits: 'Min 2 guards always',
            responsibleAgent: 'Site Security Incharge',
        },
        {
            name: 'CCTV',
            subFunction: 'Feed Uptime',
            departmentId: createdDepartments.find(d => d.name === 'CCTV').id,
            propertyId: createdProperties[0].id,
            input: 'IP stream status',
            process: 'Ping every hour',
            output: 'Online/Offline',
            thresholdLimits: 'Max downtime: 2 hrs',
            responsibleAgent: 'CCTV Vendor',
        },
        {
            name: 'CCTV',
            subFunction: 'Footage Backup',
            departmentId: createdDepartments.find(d => d.name === 'CCTV').id,
            propertyId: createdProperties[0].id,
            input: 'Drive backup logs',
            process: 'Check backup script',
            output: 'Success/Failure',
            thresholdLimits: 'Daily backup required',
            responsibleAgent: 'IT Assistant',
        },
        {
            name: 'Electricity',
            subFunction: 'Consumption Tracker',
            departmentId: createdDepartments.find(d => d.name === 'Electricity').id,
            propertyId: createdProperties[0].id,
            input: 'Energy meter data (kWh)',
            process: 'Calculate daily/monthly usage',
            output: 'kWh per day/month',
            thresholdLimits: 'Max daily: 10 kWh',
            responsibleAgent: 'Electrician',
        },
        {
            name: 'Electricity',
            subFunction: 'Load Monitoring',
            departmentId: createdDepartments.find(d => d.name === 'Electricity').id,
            propertyId: createdProperties[0].id,
            input: 'Device-level wattage',
            process: 'Aggregation from devices',
            output: 'Real-time load in watts',
            thresholdLimits: 'Max: 3000W/appliance',
            responsibleAgent: 'Technical Officer',
        },
        {
            name: 'Electricity',
            subFunction: 'Power Bill Status',
            departmentId: createdDepartments.find(d => d.name === 'Electricity').id,
            propertyId: createdProperties[0].id,
            input: 'Billing data',
            process: 'Read bill from dashboard',
            output: 'Paid/Unpaid & Due Date',
            thresholdLimits: 'Min 3 days before due',
            responsibleAgent: 'Admin',
        },
        {
            name: 'Generator',
            subFunction: 'Fuel Level Monitoring',
            departmentId: createdDepartments.find(d => d.name === 'Generator').id,
            propertyId: createdProperties[0].id,
            input: 'Manual entry or sensor',
            process: 'Log/check daily',
            output: 'Fuel % or litres',
            thresholdLimits: 'Min: 20%',
            responsibleAgent: 'Site Security Incharge',
        },
        {
            name: 'Generator',
            subFunction: 'Service Schedule',
            departmentId: createdDepartments.find(d => d.name === 'Generator').id,
            propertyId: createdProperties[0].id,
            input: 'Last service date',
            process: 'Service calendar check',
            output: 'Service due or not',
            thresholdLimits: 'Every 3 months',
            responsibleAgent: 'Generator Vendor/AMC',
        },
        {
            name: 'Generator',
            subFunction: 'Usage Hours',
            departmentId: createdDepartments.find(d => d.name === 'Generator').id,
            propertyId: createdProperties[0].id,
            input: 'Runtime logs',
            process: 'Log generator run hours',
            output: 'Daily usage in hours',
            thresholdLimits: 'Max: 2 hrs/day',
            responsibleAgent: 'Site Engineer',
        },
        {
            name: 'Internet',
            subFunction: 'Connectivity Check',
            departmentId: createdDepartments.find(d => d.name === 'Internet').id,
            propertyId: createdProperties[0].id,
            input: 'Ping status',
            process: 'Scheduled speed/ping test',
            output: 'Connected/Disconnected',
            thresholdLimits: 'Max downtime: 10 mins/day',
            responsibleAgent: 'Network Admin',
        },
        {
            name: 'Internet',
            subFunction: 'Speed Monitoring',
            departmentId: createdDepartments.find(d => d.name === 'Internet').id,
            propertyId: createdProperties[0].id,
            input: 'Speedtest result',
            process: 'Log speed hourly',
            output: 'Mbps download/upload',
            thresholdLimits: 'Min: 50 Mbps',
            responsibleAgent: 'Internet Vendor',
        },
        {
            name: 'Maintenance',
            subFunction: 'Issue Tracker',
            departmentId: createdDepartments.find(d => d.name === 'Maintenance').id,
            propertyId: createdProperties[0].id,
            input: 'Reported issue + photos',
            process: 'Log & assign issue',
            output: 'Open/In Progress/Resolved',
            thresholdLimits: 'Resolve within 3 days',
            responsibleAgent: 'Admin',
        },
        {
            name: 'Maintenance',
            subFunction: 'Scheduled Maintenance',
            departmentId: createdDepartments.find(d => d.name === 'Maintenance').id,
            propertyId: createdProperties[0].id,
            input: 'Last service record',
            process: 'Check calendar',
            output: 'Due/Not Due',
            thresholdLimits: 'Max interval: 60 days',
            responsibleAgent: 'Maintenance Supervisor',
        },
        {
            name: 'OTTs',
            subFunction: 'Subscription Tracker',
            departmentId: createdDepartments.find(d => d.name === 'OTTs').id,
            propertyId: createdProperties[0].id,
            input: 'Renewal date, cost',
            process: 'Check plan status monthly',
            output: 'Active/Inactive',
            thresholdLimits: 'Renewal 2 days before expiry',
            responsibleAgent: 'Admin',
        },
        {
            name: 'Security',
            subFunction: 'Alarm System Check',
            departmentId: createdDepartments.find(d => d.name === 'Security').id,
            propertyId: createdProperties[0].id,
            input: 'Alarm status report',
            process: 'Test alarm daily/weekly',
            output: 'Working/Not Working',
            thresholdLimits: 'Should not be inactive for 1 hour',
            responsibleAgent: 'Security Vendor',
        },
        {
            name: 'Security',
            subFunction: 'Door Lock Check',
            departmentId: createdDepartments.find(d => d.name === 'Security').id,
            propertyId: createdProperties[0].id,
            input: 'Manual or digital lock status',
            process: 'Daily inspection',
            output: 'Locked/Unlocked',
            thresholdLimits: 'Must be locked at night',
            responsibleAgent: 'Security Guard',
        },
        {
            name: 'Water',
            subFunction: 'Motor Operation',
            departmentId: createdDepartments.find(d => d.name === 'Water').id,
            propertyId: createdProperties[0].id,
            input: 'Water level + Timer settings',
            process: 'Auto/manual motor control',
            output: 'Motor status: ON/OFF',
            thresholdLimits: 'Max runtime: 30 min',
            responsibleAgent: 'Electrician/Plumber',
        },
        {
            name: 'Water',
            subFunction: 'Overhead Tank Cleanliness',
            departmentId: createdDepartments.find(d => d.name === 'Water').id,
            propertyId: createdProperties[0].id,
            input: 'Inspection date, photos',
            process: 'Monthly inspection check',
            output: 'Clean/Unclean status',
            thresholdLimits: 'Max interval: 30 days',
            responsibleAgent: 'Housekeeper',
        },
        {
            name: 'Water',
            subFunction: 'Tank Level Monitoring',
            departmentId: createdDepartments.find(d => d.name === 'Water').id,
            propertyId: createdProperties[0].id,
            input: 'Sensor reading (%, time)',
            process: 'Fetch from sensor/API',
            output: 'Current water level (%)',
            thresholdLimits: 'Min: 20%, Max: 80%',
            responsibleAgent: 'Facility Supervisor',
        },
    ];
    for (const functionData of maintenanceFunctions) {
        const maintenanceFunction = await prisma.maintenanceFunction.create({
            data: functionData,
        });
        console.log('✅ Created maintenance function:', maintenanceFunction.name, '-', maintenanceFunction.subFunction);
    }
    // Create sample activities
    await prisma.activity.create({
        data: {
            userId: admin.id,
            propertyId: createdProperties[0].id,
            action: 'SYSTEM_INITIALIZATION',
            description: 'System initialized with sample data',
            metadata: {
                seedVersion: '1.0.0',
                timestamp: new Date().toISOString(),
            },
        },
    });
    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`- Admin user: ${adminEmail} (password: ${adminPassword})`);
    console.log(`- Properties: ${createdProperties.length}`);
    console.log(`- Users: ${users.length + 1}`);
    console.log(`- Offices: ${createdOffices.length}`);
    console.log(`- Employees: ${employees.length}`);
    console.log(`- Alerts: ${alerts.length}`);
}
main()
    .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
