import { UserRole } from '@prisma/client';
import { JWTPayload, UserPermissions } from '@/types';
export declare class AuthService {
    static hashPassword(password: string): Promise<string>;
    static comparePassword(password: string, hash: string): Promise<boolean>;
    static generateAccessToken(payload: JWTPayload): string;
    static generateRefreshToken(payload: JWTPayload): string;
    static verifyAccessToken(token: string): JWTPayload;
    static verifyRefreshToken(token: string): JWTPayload;
    static getTokenExpiration(expiresIn?: string): Date;
    static getUserPermissions(role: UserRole): UserPermissions;
    static hasPermission(userRole: UserRole, requiredPermission: keyof UserPermissions, requiredValue?: boolean): boolean;
    static canAccessScreen(userRole: UserRole, screen: string): boolean;
    static canPerformAction(userRole: UserRole, action: 'create' | 'read' | 'update' | 'delete' | 'export' | 'import'): boolean;
}
