"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const client_1 = require("@prisma/client");
const JWT_SECRET = process.env.JWT_SECRET;
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';
if (!JWT_SECRET || !JWT_REFRESH_SECRET) {
    throw new Error('JWT_SECRET and JWT_REFRESH_SECRET must be defined in environment variables');
}
class AuthService {
    static async hashPassword(password) {
        const rounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
        return bcryptjs_1.default.hash(password, rounds);
    }
    static async comparePassword(password, hash) {
        return bcryptjs_1.default.compare(password, hash);
    }
    static generateAccessToken(payload) {
        return jsonwebtoken_1.default.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
    }
    static generateRefreshToken(payload) {
        return jsonwebtoken_1.default.sign(payload, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN });
    }
    static verifyAccessToken(token) {
        return jsonwebtoken_1.default.verify(token, JWT_SECRET);
    }
    static verifyRefreshToken(token) {
        return jsonwebtoken_1.default.verify(token, JWT_REFRESH_SECRET);
    }
    static getTokenExpiration(expiresIn = JWT_EXPIRES_IN) {
        const now = new Date();
        const match = expiresIn.match(/^(\d+)([smhd])$/);
        if (!match) {
            throw new Error('Invalid expires in format');
        }
        const value = parseInt(match[1]);
        const unit = match[2];
        switch (unit) {
            case 's':
                return new Date(now.getTime() + value * 1000);
            case 'm':
                return new Date(now.getTime() + value * 60 * 1000);
            case 'h':
                return new Date(now.getTime() + value * 60 * 60 * 1000);
            case 'd':
                return new Date(now.getTime() + value * 24 * 60 * 60 * 1000);
            default:
                throw new Error('Invalid time unit');
        }
    }
    static getUserPermissions(role) {
        const basePermissions = {
            canViewDashboard: false,
            canManageProperties: false,
            canManageOffice: false,
            canManageSecurity: false,
            canManageMaintenance: false,
            canManageUsers: false,
            canViewReports: false,
            canExportData: false,
            allowedScreens: [],
            allowedActions: ['read'],
        };
        switch (role) {
            case client_1.UserRole.SUPER_ADMIN:
                return {
                    canViewDashboard: true,
                    canManageProperties: true,
                    canManageOffice: true,
                    canManageSecurity: true,
                    canManageMaintenance: true,
                    canManageUsers: true,
                    canViewReports: true,
                    canExportData: true,
                    allowedScreens: [
                        'dashboard',
                        'properties',
                        'office_management',
                        'security',
                        'maintenance',
                        'users',
                        'reports',
                        'settings'
                    ],
                    allowedActions: ['create', 'read', 'update', 'delete', 'export', 'import'],
                };
            case client_1.UserRole.PROPERTY_MANAGER:
                return {
                    canViewDashboard: true,
                    canManageProperties: true,
                    canManageOffice: false,
                    canManageSecurity: true,
                    canManageMaintenance: true,
                    canManageUsers: false,
                    canViewReports: true,
                    canExportData: true,
                    allowedScreens: [
                        'dashboard',
                        'properties',
                        'security',
                        'maintenance',
                        'reports'
                    ],
                    allowedActions: ['create', 'read', 'update', 'delete', 'export'],
                };
            case client_1.UserRole.OFFICE_MANAGER:
                return {
                    canViewDashboard: true,
                    canManageProperties: false,
                    canManageOffice: true,
                    canManageSecurity: false,
                    canManageMaintenance: false,
                    canManageUsers: false,
                    canViewReports: true,
                    canExportData: true,
                    allowedScreens: [
                        'dashboard',
                        'office_management',
                        'reports'
                    ],
                    allowedActions: ['create', 'read', 'update', 'export'],
                };
            case client_1.UserRole.SECURITY_PERSONNEL:
                return {
                    canViewDashboard: true,
                    canManageProperties: false,
                    canManageOffice: false,
                    canManageSecurity: true,
                    canManageMaintenance: false,
                    canManageUsers: false,
                    canViewReports: false,
                    canExportData: false,
                    allowedScreens: [
                        'dashboard',
                        'security'
                    ],
                    allowedActions: ['read', 'update'],
                };
            case client_1.UserRole.MAINTENANCE_STAFF:
                return {
                    canViewDashboard: true,
                    canManageProperties: false,
                    canManageOffice: false,
                    canManageSecurity: false,
                    canManageMaintenance: true,
                    canManageUsers: false,
                    canViewReports: false,
                    canExportData: false,
                    allowedScreens: [
                        'dashboard',
                        'maintenance'
                    ],
                    allowedActions: ['read', 'update'],
                };
            case client_1.UserRole.CONSTRUCTION_SUPERVISOR:
                return {
                    canViewDashboard: true,
                    canManageProperties: false,
                    canManageOffice: true,
                    canManageSecurity: false,
                    canManageMaintenance: false,
                    canManageUsers: false,
                    canViewReports: true,
                    canExportData: true,
                    allowedScreens: [
                        'dashboard',
                        'office_management',
                        'reports'
                    ],
                    allowedActions: ['create', 'read', 'update', 'export'],
                };
            default:
                return basePermissions;
        }
    }
    static hasPermission(userRole, requiredPermission, requiredValue = true) {
        const permissions = this.getUserPermissions(userRole);
        return permissions[requiredPermission] === requiredValue;
    }
    static canAccessScreen(userRole, screen) {
        const permissions = this.getUserPermissions(userRole);
        return permissions.allowedScreens.includes(screen);
    }
    static canPerformAction(userRole, action) {
        const permissions = this.getUserPermissions(userRole);
        return permissions.allowedActions.includes(action);
    }
}
exports.AuthService = AuthService;
