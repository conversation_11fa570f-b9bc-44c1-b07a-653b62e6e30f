import { Request, Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                role: UserRole;
                assignedProperties: string[];
            };
        }
    }
}
export interface AuthenticatedRequest extends Request {
    user: {
        id: string;
        email: string;
        role: UserRole;
        assignedProperties: string[];
    };
}
export declare const authenticate: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const authorize: (requiredRoles?: UserRole[], requiredPermission?: keyof import("@/types").UserPermissions) => (req: Request, res: Response, next: NextFunction) => void;
export declare const requirePropertyAccess: (propertyIdParam?: string) => (req: Request, res: Response, next: NextFunction) => void;
export declare const requireAction: (action: "create" | "read" | "update" | "delete" | "export" | "import") => (req: Request, res: Response, next: NextFunction) => void;
export declare const optionalAuth: (req: Request, res: Response, next: NextFunction) => Promise<void>;
