import { Request, Response, NextFunction } from 'express';
export declare class AppError extends Error {
    statusCode: number;
    isOperational: boolean;
    code?: string;
    constructor(message: string, statusCode?: number, code?: string);
}
export declare const errorHandler: (error: Error, req: Request, res: Response, next: NextFunction) => void;
export declare const notFoundHandler: (req: Request, res: Response, next: NextFunction) => void;
export declare const asyncHandler: (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateRequest: (schema: any, source?: "body" | "query" | "params") => (req: Request, res: Response, next: NextFunction) => void;
export declare const rateLimitHandler: (req: Request, res: Response, next: NextFunction, retryAfter?: number) => void;
export declare const corsError<PERSON><PERSON><PERSON>: (req: Request, res: Response, next: NextFunction) => void;
