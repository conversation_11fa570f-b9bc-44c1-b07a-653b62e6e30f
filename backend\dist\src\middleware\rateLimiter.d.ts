import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory, RateLimiterRes } from 'rate-limiter-flexible';
declare const rateLimiters: {
    general: RateLimiterMemory;
    auth: RateLimiterMemory;
    write: RateLimiterMemory;
    upload: RateLimiterMemory;
    user: RateLimiterMemory;
};
export declare const createRateLimiter: (limiterName: keyof typeof rateLimiters, customMessage?: string) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const generalRateLimit: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const authRateLimit: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const writeRateLimit: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const uploadRateLimit: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const userRateLimit: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const conditionalRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const endpointSpecificRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const resetRateLimit: (key: string, limiterName: keyof typeof rateLimiters) => Promise<void>;
export declare const getRateLimitStatus: (key: string, limiterName: keyof typeof rateLimiters) => Promise<RateLimiterRes | null>;
export declare const addRateLimitHeaders: (limiterName: keyof typeof rateLimiters) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
export {};
