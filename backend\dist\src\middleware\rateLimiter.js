"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addRateLimitHeaders = exports.getRateLimitStatus = exports.resetRateLimit = exports.endpointSpecificRateLimit = exports.conditionalRateLimit = exports.userRateLimit = exports.uploadRateLimit = exports.writeRateLimit = exports.authRateLimit = exports.generalRateLimit = exports.createRateLimiter = void 0;
const rate_limiter_flexible_1 = require("rate-limiter-flexible");
// Rate limiter configurations
const rateLimiters = {
    // General API rate limiter
    general: new rate_limiter_flexible_1.RateLimiterMemory({
        points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
        duration: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000') / 1000, // 15 minutes
        blockDuration: 60, // Block for 1 minute
    }),
    // Authentication rate limiter (stricter)
    auth: new rate_limiter_flexible_1.RateLimiterMemory({
        points: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '5'),
        duration: 900, // 15 minutes
        blockDuration: 900, // Block for 15 minutes
    }),
    // Write operations rate limiter
    write: new rate_limiter_flexible_1.RateLimiterMemory({
        points: 30,
        duration: 900, // 15 minutes
        blockDuration: 60, // Block for 1 minute
    }),
    // File upload rate limiter
    upload: new rate_limiter_flexible_1.RateLimiterMemory({
        points: 10,
        duration: 3600, // 1 hour
        blockDuration: 300, // Block for 5 minutes
    }),
    // Per-user rate limiter for authenticated requests
    user: new rate_limiter_flexible_1.RateLimiterMemory({
        points: 200,
        duration: 900, // 15 minutes
        blockDuration: 60, // Block for 1 minute
    }),
};
// Key generators for different rate limiters
const keyGenerators = {
    general: (req) => req.ip,
    auth: (req) => req.ip,
    write: (req) => req.ip,
    upload: (req) => req.ip,
    user: (req) => req.user?.id || req.ip,
};
// Generic rate limiter middleware factory
const createRateLimiter = (limiterName, customMessage) => {
    return async (req, res, next) => {
        const limiter = rateLimiters[limiterName];
        const keyGenerator = keyGenerators[limiterName];
        const key = keyGenerator(req);
        try {
            const resRateLimiter = await limiter.consume(key);
            // Add rate limit headers
            res.set({
                'X-RateLimit-Limit': limiter.points.toString(),
                'X-RateLimit-Remaining': resRateLimiter.remainingPoints?.toString() || '0',
                'X-RateLimit-Reset': new Date(Date.now() + (resRateLimiter.msBeforeNext || 60000)).toISOString(),
            });
            next();
        }
        catch (rateLimiterRes) {
            const result = rateLimiterRes;
            const msBeforeNext = result.msBeforeNext || 60000; // Default to 1 minute
            const retryAfter = Math.round(msBeforeNext / 1000);
            res.set({
                'X-RateLimit-Limit': limiter.points.toString(),
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
                'Retry-After': retryAfter.toString(),
            });
            res.status(429).json({
                success: false,
                error: 'RATE_LIMIT_EXCEEDED',
                message: customMessage || 'Too many requests. Please try again later.',
                timestamp: new Date().toISOString(),
                path: req.path,
                retryAfter,
            });
        }
    };
};
exports.createRateLimiter = createRateLimiter;
// Specific rate limiter middlewares
exports.generalRateLimit = (0, exports.createRateLimiter)('general');
exports.authRateLimit = (0, exports.createRateLimiter)('auth', 'Too many authentication attempts. Please try again in 15 minutes.');
exports.writeRateLimit = (0, exports.createRateLimiter)('write', 'Too many write operations. Please try again later.');
exports.uploadRateLimit = (0, exports.createRateLimiter)('upload', 'Too many file uploads. Please try again later.');
exports.userRateLimit = (0, exports.createRateLimiter)('user');
// Conditional rate limiter based on request method
const conditionalRateLimit = (req, res, next) => {
    const method = req.method.toUpperCase();
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
        (0, exports.writeRateLimit)(req, res, next);
    }
    else {
        (0, exports.generalRateLimit)(req, res, next);
    }
};
exports.conditionalRateLimit = conditionalRateLimit;
// IP-based rate limiter with different limits for different endpoints
const endpointSpecificRateLimit = (req, res, next) => {
    const path = req.path.toLowerCase();
    if (path.includes('/auth/')) {
        (0, exports.authRateLimit)(req, res, next);
    }
    else if (path.includes('/upload') || path.includes('/files')) {
        (0, exports.uploadRateLimit)(req, res, next);
    }
    else if (req.user) {
        (0, exports.userRateLimit)(req, res, next);
    }
    else {
        (0, exports.generalRateLimit)(req, res, next);
    }
};
exports.endpointSpecificRateLimit = endpointSpecificRateLimit;
// Reset rate limiter for a specific key (useful for testing or admin actions)
const resetRateLimit = async (key, limiterName) => {
    const limiter = rateLimiters[limiterName];
    await limiter.delete(key);
};
exports.resetRateLimit = resetRateLimit;
// Get rate limit status for a key
const getRateLimitStatus = async (key, limiterName) => {
    const limiter = rateLimiters[limiterName];
    return limiter.get(key);
};
exports.getRateLimitStatus = getRateLimitStatus;
// Middleware to add rate limit info to response headers (for monitoring)
const addRateLimitHeaders = (limiterName) => {
    return async (req, res, next) => {
        const limiter = rateLimiters[limiterName];
        const keyGenerator = keyGenerators[limiterName];
        const key = keyGenerator(req);
        try {
            const status = await limiter.get(key);
            if (status) {
                res.set({
                    'X-RateLimit-Limit': limiter.points.toString(),
                    'X-RateLimit-Remaining': status.remainingPoints?.toString() || '0',
                    'X-RateLimit-Reset': new Date(Date.now() + (status.msBeforeNext || 60000)).toISOString(),
                });
            }
            else {
                res.set({
                    'X-RateLimit-Limit': limiter.points.toString(),
                    'X-RateLimit-Remaining': limiter.points.toString(),
                });
            }
        }
        catch (error) {
            console.warn('Failed to get rate limit status:', error);
        }
        next();
    };
};
exports.addRateLimitHeaders = addRateLimitHeaders;
