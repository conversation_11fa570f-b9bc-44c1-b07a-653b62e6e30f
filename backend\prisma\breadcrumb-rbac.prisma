// Breadcrumb-Based RBAC Schema

// Breadcrumb path definitions
model BreadcrumbPath {
  id          String   @id @default(uuid())
  path        String   @unique // e.g., "/properties/{propertyId}/systems/security/cctv"
  name        String   // Display name e.g., "CCTV Management"
  description String?
  level       Int      // Hierarchy level (0 = root, 1 = first level, etc.)
  parentPath  String?  // Parent breadcrumb path
  isActive    Boolean  @default(true)
  metadata    Json?    @default("{}") // Additional path metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  parent           BreadcrumbPath?       @relation("PathHierarchy", fields: [parentPath], references: [path])
  children         BreadcrumbPath[]      @relation("PathHierarchy")
  pathPermissions  PathPermission[]
  pathComponents   PathComponent[]

  @@map("breadcrumb_paths")
}

// Role permissions for breadcrumb paths
model PathPermission {
  id           String   @id @default(uuid())
  pathId       String
  roleId       String
  accessLevel  String   @default("none") // 'full', 'read', 'write', 'restricted', 'none'
  permissions  Json     @default("{}") // Specific permissions object
  conditions   Json?    @default("{}") // ABAC conditions
  restrictions Json?    @default("{}") // Access restrictions
  isInherited  Boolean  @default(false) // Inherited from parent path
  priority     Int      @default(0) // Permission priority
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  path BreadcrumbPath @relation(fields: [pathId], references: [id], onDelete: Cascade)
  role Role           @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([pathId, roleId])
  @@map("path_permissions")
}

// UI Components mapped to breadcrumb paths
model PathComponent {
  id          String   @id @default(uuid())
  pathId      String
  componentId String   // Unique component identifier
  name        String   // Component display name
  type        String   // 'widget', 'button', 'form', 'table', 'card', etc.
  section     String?  // Section within the path
  permissions Json     @default("{}") // Component-specific permissions
  metadata    Json?    @default("{}") // Component metadata
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  path                BreadcrumbPath              @relation(fields: [pathId], references: [id], onDelete: Cascade)
  componentPermissions ComponentPermission[]

  @@unique([pathId, componentId])
  @@map("path_components")
}

// Component-level permissions
model ComponentPermission {
  id            String   @id @default(uuid())
  componentId   String
  roleId        String
  accessLevel   String   @default("none") // 'visible', 'hidden', 'disabled', 'read_only'
  permissions   Json     @default("{}") // Specific component permissions
  restrictions  Json?    @default("{}") // Component restrictions
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  component PathComponent @relation(fields: [componentId], references: [id], onDelete: Cascade)
  role      Role          @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([componentId, roleId])
  @@map("component_permissions")
}

// Breadcrumb navigation history and audit
model NavigationAudit {
  id          String   @id @default(uuid())
  userId      String
  path        String   // Breadcrumb path accessed
  action      String   // 'view', 'edit', 'create', 'delete', etc.
  granted     Boolean  // Whether access was granted
  reason      String?  // Reason for denial if applicable
  context     Json?    @default("{}") // Request context
  ipAddress   String?
  userAgent   String?
  sessionId   String?
  createdAt   DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("navigation_audits")
}

// Dynamic breadcrumb generation rules
model BreadcrumbRule {
  id          String   @id @default(uuid())
  pattern     String   @unique // Path pattern e.g., "/properties/{propertyId}/systems/{systemType}"
  template    String   // Display template e.g., "Property {propertyName} > {systemType} Management"
  resolver    String   // Function to resolve dynamic parts
  priority    Int      @default(0)
  isActive    Boolean  @default(true)
  metadata    Json?    @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("breadcrumb_rules")
}

// Path-based data filters
model PathDataFilter {
  id         String   @id @default(uuid())
  pathId     String
  roleId     String
  resource   String   // Database table/model name
  filterType String   // 'where', 'select', 'join', 'limit'
  conditions Json     // Filter conditions
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  path BreadcrumbPath @relation(fields: [pathId], references: [id], onDelete: Cascade)
  role Role           @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([pathId, roleId, resource, filterType])
  @@map("path_data_filters")
}

// Update existing models to include breadcrumb relationships
// Add these fields to existing User model:
// navigationAudits NavigationAudit[]

// Add these fields to existing Role model:
// pathPermissions     PathPermission[]
// componentPermissions ComponentPermission[]
// pathDataFilters     PathDataFilter[]
