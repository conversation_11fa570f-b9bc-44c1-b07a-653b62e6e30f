// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OTTService _$OTTServiceFromJson(Map<String, dynamic> json) => OTTService(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      platform: json['platform'] as String,
      plan: json['plan'] as String,
      loginId: json['loginId'] as String?,
      password: json['password'] as String?,
      nextPayment: json['nextPayment'] as String?,
      status: json['status'] as String,
      isActive: json['isActive'] as bool,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$OTTServiceToJson(OTTService instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'platform': instance.platform,
      'plan': instance.plan,
      'loginId': instance.loginId,
      'password': instance.password,
      'nextPayment': instance.nextPayment,
      'status': instance.status,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

CreateOTTServiceRequest _$CreateOTTServiceRequestFromJson(
        Map<String, dynamic> json) =>
    CreateOTTServiceRequest(
      platform: json['platform'] as String,
      plan: json['plan'] as String,
      loginId: json['loginId'] as String?,
      password: json['password'] as String?,
      nextPayment: json['nextPayment'] as String?,
      status: json['status'] as String?,
    );

Map<String, dynamic> _$CreateOTTServiceRequestToJson(
        CreateOTTServiceRequest instance) =>
    <String, dynamic>{
      'platform': instance.platform,
      'plan': instance.plan,
      'loginId': instance.loginId,
      'password': instance.password,
      'nextPayment': instance.nextPayment,
      'status': instance.status,
    };

UpdateOTTServiceRequest _$UpdateOTTServiceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateOTTServiceRequest(
      platform: json['platform'] as String?,
      plan: json['plan'] as String?,
      loginId: json['loginId'] as String?,
      password: json['password'] as String?,
      nextPayment: json['nextPayment'] as String?,
      status: json['status'] as String?,
    );

Map<String, dynamic> _$UpdateOTTServiceRequestToJson(
        UpdateOTTServiceRequest instance) =>
    <String, dynamic>{
      'platform': instance.platform,
      'plan': instance.plan,
      'loginId': instance.loginId,
      'password': instance.password,
      'nextPayment': instance.nextPayment,
      'status': instance.status,
    };

OTTServiceResponse _$OTTServiceResponseFromJson(Map<String, dynamic> json) =>
    OTTServiceResponse(
      success: json['success'] as bool,
      data: json['data'] == null
          ? null
          : OTTService.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String?,
      error: json['error'] as String?,
      timestamp: json['timestamp'] as String,
    );

Map<String, dynamic> _$OTTServiceResponseToJson(OTTServiceResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data,
      'message': instance.message,
      'error': instance.error,
      'timestamp': instance.timestamp,
    };

OTTServicesListResponse _$OTTServicesListResponseFromJson(
        Map<String, dynamic> json) =>
    OTTServicesListResponse(
      success: json['success'] as bool,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => OTTService.fromJson(e as Map<String, dynamic>))
          .toList(),
      pagination: json['pagination'] == null
          ? null
          : PaginationInfo.fromJson(json['pagination'] as Map<String, dynamic>),
      message: json['message'] as String?,
      error: json['error'] as String?,
      timestamp: json['timestamp'] as String,
    );

Map<String, dynamic> _$OTTServicesListResponseToJson(
        OTTServicesListResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data,
      'pagination': instance.pagination,
      'message': instance.message,
      'error': instance.error,
      'timestamp': instance.timestamp,
    };

PaginationInfo _$PaginationInfoFromJson(Map<String, dynamic> json) =>
    PaginationInfo(
      page: (json['page'] as num).toInt(),
      limit: (json['limit'] as num).toInt(),
      total: (json['total'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
      hasNext: json['hasNext'] as bool,
      hasPrev: json['hasPrev'] as bool,
    );

Map<String, dynamic> _$PaginationInfoToJson(PaginationInfo instance) =>
    <String, dynamic>{
      'page': instance.page,
      'limit': instance.limit,
      'total': instance.total,
      'totalPages': instance.totalPages,
      'hasNext': instance.hasNext,
      'hasPrev': instance.hasPrev,
    };
