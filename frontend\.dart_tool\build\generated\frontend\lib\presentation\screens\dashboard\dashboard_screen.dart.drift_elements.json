{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:go_router/go_router.dart", "transitive": false}, {"uri": "package:fl_chart/fl_chart.dart", "transitive": false}, {"uri": "package:frontend/core/theme/app_theme.dart", "transitive": false}, {"uri": "package:frontend/core/services/service_locator.dart", "transitive": false}, {"uri": "package:frontend/presentation/routes/app_router.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/main/main_navigation_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/providers/dashboard_providers.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/dashboard/dashboard_v2_screen.dart", "transitive": false}], "elements": []}