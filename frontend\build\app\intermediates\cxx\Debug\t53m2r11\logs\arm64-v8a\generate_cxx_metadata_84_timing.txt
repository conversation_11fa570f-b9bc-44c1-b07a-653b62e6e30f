# C/C++ build system timings
generate_cxx_metadata
  [gap of 124ms]
  create-invalidation-state 40ms
  execute-generate-process
    [gap of 11ms]
    exec-configure 24543ms
    [gap of 105ms]
  execute-generate-process completed in 24659ms
  [gap of 134ms]
  write-metadata-json-to-file 28ms
generate_cxx_metadata completed in 25040ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 32ms]
  create-invalidation-state 78ms
  [gap of 30ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 155ms

