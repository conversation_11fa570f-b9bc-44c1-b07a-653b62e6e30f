
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/api_constants.dart';
import '../../data/models/api_response.dart';

class ApiClient {
  static final ApiClient _instance = ApiClient._internal();
  factory ApiClient() => _instance;
  ApiClient._internal();

  late Dio _dio;
  String? _accessToken;
  String? _refreshToken;

  void initialize() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConstants.apiBaseUrl,
      connectTimeout: const Duration(milliseconds: ApiConstants.connectTimeout),
      receiveTimeout: const Duration(milliseconds: ApiConstants.receiveTimeout),
      sendTimeout: const Duration(milliseconds: ApiConstants.sendTimeout),
      headers: {
        ApiConstants.contentType: ApiConstants.applicationJson,
      },
    ));

    // Add interceptors
    _dio.interceptors.add(_AuthInterceptor());
    _dio.interceptors.add(_ErrorInterceptor());
    
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
      ));
    }

    // Load stored tokens
    _loadTokens();
  }

  Future<void> _loadTokens() async {
    final prefs = await SharedPreferences.getInstance();
    _accessToken = prefs.getString(ApiConstants.accessTokenKey);
    _refreshToken = prefs.getString(ApiConstants.refreshTokenKey);
  }

  Future<void> setTokens(String accessToken, String refreshToken) async {
    _accessToken = accessToken;
    _refreshToken = refreshToken;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(ApiConstants.accessTokenKey, accessToken);
    await prefs.setString(ApiConstants.refreshTokenKey, refreshToken);
  }

  Future<void> clearTokens() async {
    _accessToken = null;
    _refreshToken = null;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(ApiConstants.accessTokenKey);
    await prefs.remove(ApiConstants.refreshTokenKey);
    await prefs.remove(ApiConstants.userDataKey);
  }

  bool get isAuthenticated => _accessToken != null;
  String? get accessToken => _accessToken;
  String? get refreshToken => _refreshToken;

  // HTTP Methods
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> delete<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(path, queryParameters: queryParameters);
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> upload<T>(
    String path, {
    required FormData formData,
    T Function(dynamic)? fromJson,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: formData,
        onSendProgress: onSendProgress,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  ApiResponse<T> _handleResponse<T>(Response response, T Function(dynamic)? fromJson) {
    if (response.statusCode! >= 200 && response.statusCode! < 300) {
      final responseData = response.data;
      
      if (responseData is Map<String, dynamic>) {
        if (responseData['success'] == true) {
          final data = responseData['data'];
          T? parsedData;
          
          if (fromJson != null && data != null) {
            parsedData = fromJson(data);
          } else {
            parsedData = data as T?;
          }
          
          return ApiResponse<T>(
            success: true,
            data: parsedData,
            message: responseData['message'],
            timestamp: responseData['timestamp'] ?? DateTime.now().toIso8601String(),
          );
        } else {
          return ApiResponse<T>(
            success: false,
            error: responseData['error'] ?? 'Unknown error',
            message: responseData['message'],
            timestamp: responseData['timestamp'] ?? DateTime.now().toIso8601String(),
          );
        }
      }
    }
    
    return ApiResponse<T>(
      success: false,
      error: 'INVALID_RESPONSE',
      message: 'Invalid response format',
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  ApiResponse<T> _handleError<T>(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return ApiResponse<T>(
            success: false,
            error: ApiConstants.errorTimeout,
            message: 'Request timeout. Please try again.',
            timestamp: DateTime.now().toIso8601String(),
          );
        
        case DioExceptionType.connectionError:
          return ApiResponse<T>(
            success: false,
            error: ApiConstants.errorNetworkError,
            message: 'Network error. Please check your connection.',
            timestamp: DateTime.now().toIso8601String(),
          );
        
        case DioExceptionType.badResponse:
          final responseData = error.response?.data;
          if (responseData is Map<String, dynamic>) {
            return ApiResponse<T>(
              success: false,
              error: responseData['error'] ?? 'HTTP_ERROR',
              message: responseData['message'] ?? 'Request failed',
              timestamp: responseData['timestamp'] ?? DateTime.now().toIso8601String(),
            );
          }
          return ApiResponse<T>(
            success: false,
            error: 'HTTP_ERROR',
            message: 'HTTP ${error.response?.statusCode}: ${error.response?.statusMessage}',
            timestamp: DateTime.now().toIso8601String(),
          );
        
        default:
          return ApiResponse<T>(
            success: false,
            error: ApiConstants.errorServerError,
            message: error.message ?? 'An unexpected error occurred',
            timestamp: DateTime.now().toIso8601String(),
          );
      }
    }
    
    return ApiResponse<T>(
      success: false,
      error: ApiConstants.errorServerError,
      message: error.toString(),
      timestamp: DateTime.now().toIso8601String(),
    );
  }
}

class _AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final apiClient = ApiClient();
    if (apiClient.accessToken != null) {
      options.headers[ApiConstants.authHeader] = 'Bearer ${apiClient.accessToken}';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      final apiClient = ApiClient();
      if (apiClient.refreshToken != null) {
        try {
          // Try to refresh token
          final refreshResponse = await apiClient._dio.post(
            ApiConstants.refresh,
            data: {'refreshToken': apiClient.refreshToken},
          );
          
          if (refreshResponse.statusCode == 200) {
            final data = refreshResponse.data['data'];
            await apiClient.setTokens(
              data['accessToken'],
              data['refreshToken'],
            );
            
            // Retry original request
            final opts = err.requestOptions;
            opts.headers[ApiConstants.authHeader] = 'Bearer ${data['accessToken']}';
            final cloneReq = await apiClient._dio.fetch(opts);
            return handler.resolve(cloneReq);
          }
        } catch (e) {
          // Refresh failed, clear tokens
          await apiClient.clearTokens();
        }
      }
    }
    handler.next(err);
  }
}

class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      print('API Error: ${err.message}');
      print('Response: ${err.response?.data}');
    }
    handler.next(err);
  }
}
