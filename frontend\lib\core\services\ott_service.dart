import '../../data/models/api_response.dart';
import '../../data/models/ott_service.dart' as ott_models;
import '../constants/api_constants.dart';
import 'api_client.dart';

class OTTService {
  final ApiClient _apiClient = ApiClient();

  // Check if user is authenticated
  bool get isAuthenticated => _apiClient.isAuthenticated;

  // Get list of OTT services for a property
  Future<ApiResponse<List<ott_models.OTTService>>> getOTTServices({
    required String propertyId,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      final path = ApiConstants.ottServices.replaceAll('{propertyId}', propertyId);

      final response = await _apiClient.get<ott_models.OTTServicesListResponse>(
        path,
        queryParameters: queryParams,
        fromJson: (json) => ott_models.OTTServicesListResponse.fromJson(json as Map<String, dynamic>),
      );

      if (response.success && response.data != null) {
        return ApiResponse<List<ott_models.OTTService>>(
          success: true,
          data: response.data!.data ?? [],
          message: response.data!.message,
          timestamp: response.data!.timestamp,
        );
      } else {
        return ApiResponse<List<ott_models.OTTService>>(
          success: false,
          error: response.data?.error ?? ApiConstants.errorServerError,
          message: response.data?.message ?? 'Failed to fetch OTT services',
          timestamp: DateTime.now().toIso8601String(),
        );
      }
    } catch (e) {
      return ApiResponse<List<ott_models.OTTService>>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch OTT services: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Create new OTT service
  Future<ApiResponse<ott_models.OTTService>> createOTTService({
    required String propertyId,
    required ott_models.CreateOTTServiceRequest request,
  }) async {
    try {
      final path = ApiConstants.ottServices.replaceAll('{propertyId}', propertyId);

      final response = await _apiClient.post<ott_models.OTTServiceResponse>(
        path,
        data: request.toJson(),
        fromJson: (json) => ott_models.OTTServiceResponse.fromJson(json as Map<String, dynamic>),
      );

      if (response.success && response.data != null && response.data!.data != null) {
        return ApiResponse<ott_models.OTTService>(
          success: true,
          data: response.data!.data!,
          message: response.data!.message,
          timestamp: response.data!.timestamp,
        );
      } else {
        return ApiResponse<ott_models.OTTService>(
          success: false,
          error: response.data?.error ?? ApiConstants.errorServerError,
          message: response.data?.message ?? 'Failed to create OTT service',
          timestamp: DateTime.now().toIso8601String(),
        );
      }
    } catch (e) {
      return ApiResponse<ott_models.OTTService>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to create OTT service: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Update OTT service
  Future<ApiResponse<ott_models.OTTService>> updateOTTService({
    required String propertyId,
    required String serviceId,
    required ott_models.UpdateOTTServiceRequest request,
  }) async {
    try {
      final path = ApiConstants.ottServiceDetail
          .replaceAll('{propertyId}', propertyId)
          .replaceAll('{serviceId}', serviceId);

      final response = await _apiClient.put<ott_models.OTTServiceResponse>(
        path,
        data: request.toJson(),
        fromJson: (json) => ott_models.OTTServiceResponse.fromJson(json as Map<String, dynamic>),
      );

      if (response.success && response.data != null && response.data!.data != null) {
        return ApiResponse<ott_models.OTTService>(
          success: true,
          data: response.data!.data!,
          message: response.data!.message,
          timestamp: response.data!.timestamp,
        );
      } else {
        return ApiResponse<ott_models.OTTService>(
          success: false,
          error: response.data?.error ?? ApiConstants.errorServerError,
          message: response.data?.message ?? 'Failed to update OTT service',
          timestamp: DateTime.now().toIso8601String(),
        );
      }
    } catch (e) {
      return ApiResponse<ott_models.OTTService>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to update OTT service: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Delete OTT service
  Future<ApiResponse<void>> deleteOTTService({
    required String propertyId,
    required String serviceId,
  }) async {
    try {
      final path = ApiConstants.ottServiceDetail
          .replaceAll('{propertyId}', propertyId)
          .replaceAll('{serviceId}', serviceId);

      final response = await _apiClient.delete<Map<String, dynamic>>(
        path,
        fromJson: (json) => json as Map<String, dynamic>,
      );

      return ApiResponse<void>(
        success: response.success,
        error: response.error,
        message: response.message,
        timestamp: response.timestamp,
      );
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to delete OTT service: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get OTT statuses for dropdown
  List<String> getOTTStatuses() {
    return ApiConstants.ottStatuses;
  }

  // Helper method to get OTT status display name
  String getOTTStatusDisplayName(String status) {
    switch (status) {
      case 'PENDING':
        return 'Pending';
      case 'ACTIVE':
        return 'Active';
      case 'EXPIRED':
        return 'Expired';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status;
    }
  }

  // Helper method to get status color
  String getStatusColor(String status) {
    switch (status) {
      case 'ACTIVE':
        return 'green';
      case 'PENDING':
        return 'orange';
      case 'EXPIRED':
        return 'red';
      case 'CANCELLED':
        return 'grey';
      default:
        return 'grey';
    }
  }

  // Helper method to format next payment date
  String formatNextPaymentDate(String? nextPayment) {
    if (nextPayment == null) return 'Not set';
    
    try {
      final date = DateTime.parse(nextPayment);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'Invalid date';
    }
  }

  // Helper method to check if payment is due soon
  bool isPaymentDueSoon(String? nextPayment) {
    if (nextPayment == null) return false;
    
    try {
      final paymentDate = DateTime.parse(nextPayment);
      final now = DateTime.now();
      final daysUntilPayment = paymentDate.difference(now).inDays;
      return daysUntilPayment <= 30 && daysUntilPayment >= 0;
    } catch (e) {
      return false;
    }
  }

  // Helper method to check if payment is overdue
  bool isPaymentOverdue(String? nextPayment) {
    if (nextPayment == null) return false;
    
    try {
      final paymentDate = DateTime.parse(nextPayment);
      return paymentDate.isBefore(DateTime.now());
    } catch (e) {
      return false;
    }
  }

  // Helper method to get days until payment
  int getDaysUntilPayment(String? nextPayment) {
    if (nextPayment == null) return -1;
    
    try {
      final paymentDate = DateTime.parse(nextPayment);
      final now = DateTime.now();
      return paymentDate.difference(now).inDays;
    } catch (e) {
      return -1;
    }
  }
}
