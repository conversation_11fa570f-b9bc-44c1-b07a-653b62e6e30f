import 'package:flutter/foundation.dart';
import 'api_client.dart';
import 'auth_service.dart';
import 'property_service.dart';
import 'dashboard_service.dart';
import 'office_service.dart';
import 'notification_service.dart';
import 'file_service.dart';


class ServiceLocator {
  static final ServiceLocator _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;
  ServiceLocator._internal();

  bool _isInitialized = false;

  // Service instances
  late final ApiClient _apiClient;
  late final AuthService _authService;
  late final PropertyService _propertyService;
  late final DashboardService _dashboardService;
  late final OfficeService _officeService;
  late final NotificationService _notificationService;
  late final FileService _fileService;


  // Getters for services
  ApiClient get apiClient => _apiClient;
  AuthService get authService => _authService;
  PropertyService get propertyService => _propertyService;
  DashboardService get dashboardService => _dashboardService;
  OfficeService get officeService => _officeService;
  NotificationService get notificationService => _notificationService;
  FileService get fileService => _fileService;


  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('Initializing services...');
      }

      // Initialize API client first
      _apiClient = ApiClient();
      _apiClient.initialize();

      // Initialize auth service
      _authService = AuthService();
      await _authService.initialize();

      // Initialize other services
      _propertyService = PropertyService();
      _dashboardService = DashboardService();
      _officeService = OfficeService();
      _notificationService = NotificationService();
      _fileService = FileService();



      _isInitialized = true;

      if (kDebugMode) {
        print('Services initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing services: $e');
      }
      rethrow;
    }
  }

  Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      if (kDebugMode) {
        print('Disposing services...');
      }



      // Clear auth data
      await _authService.logout();

      _isInitialized = false;

      if (kDebugMode) {
        print('Services disposed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error disposing services: $e');
      }
    }
  }

  // Authentication helpers
  Future<void> onLoginSuccess() async {
    // Authentication successful - services are ready
  }

  Future<void> onLogout() async {
    // Clear auth data
    await _authService.logout();
  }



  // Service status
  Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'isAuthenticated': _authService.isAuthenticated,
      'currentUser': _authService.currentUser?.toJson(),
    };
  }

  // Error handling
  void handleApiError(dynamic error) {
    if (kDebugMode) {
      print('API Error: $error');
    }
    
    // Handle specific error types
    if (error is Map<String, dynamic>) {
      final errorCode = error['error'];
      
      switch (errorCode) {
        case 'UNAUTHORIZED':
          // Token expired or invalid
          onLogout();
          break;
        case 'RATE_LIMIT_EXCEEDED':
          // Show rate limit message
          break;
        case 'NETWORK_ERROR':
          // Show network error message
          break;
        default:
          // Show generic error message
          break;
      }
    }
  }

  // Connectivity handling
  void onConnectivityChanged(bool isConnected) {
    if (isConnected) {
      // Handle online mode
      if (kDebugMode) {
        print('App is online');
      }
    } else {
      // Handle offline mode
      if (kDebugMode) {
        print('App is offline');
      }
    }
  }

  // Background task handling
  void onAppResumed() {
    // Refresh user data
    if (_authService.isAuthenticated) {
      _authService.getCurrentUser();
    }
  }

  void onAppPaused() {
    // Handle app pause
  }

  // Notification handling
  void setupNotificationListeners() {
    // Setup local notification listeners
    // This can be used for scheduled notifications or other local events
  }

  // Development helpers
  void enableDebugMode() {
    if (kDebugMode) {
      // Enable additional logging
      print('Debug mode enabled');
    }
  }
}

// Global service locator instance
final serviceLocator = ServiceLocator();
